import * as React from "@rbxts/react";
import { SplashScreen } from "./SplashScreen";
import { useSplashScreen } from "./useSplashScreen";

/**
 * Example component showing how to use the SplashScreen system
 * This would typically be used in your main App component
 */
export function SplashScreenExample() {
    const { state, startLoading, hide, setupDefaultTasks, manager } = useSplashScreen();
    const [hasStarted, setHasStarted] = React.useState(false);

    // Setup and start loading when component mounts
    React.useEffect(() => {
        if (!hasStarted) {
            // Setup default core framework loading tasks
            setupDefaultTasks();
            
            // You can also add custom loading tasks
            manager.addLoadingTask({
                name: "Loading Game Data...",
                weight: 2,
                task: async () => {
                    // Simulate loading game data
                    await new Promise(resolve => task.delay(1, resolve));
                }
            });

            manager.addLoadingTask({
                name: "Connecting to Server...",
                weight: 1,
                task: async () => {
                    // Simulate server connection
                    await new Promise(resolve => task.delay(0.5, resolve));
                }
            });

            // Start the loading process
            startLoading();
            setHasStarted(true);
        }
    }, [hasStarted, setupDefaultTasks, manager, startLoading]);

    const handleLoadingComplete = React.useCallback(() => {
        // Called when loading is complete and splash screen should hide
        hide();
        print("🎉 Splash screen loading completed!");
    }, [hide]);

    return (
        <>
            <SplashScreen
                isVisible={state.isVisible}
                loadingProgress={state.loadingProgress}
                loadingText={state.loadingText}
                onLoadingComplete={handleLoadingComplete}
            />
            
            {/* Your main app content goes here */}
            {!state.isVisible && (
                <frame
                    Size={new UDim2(1, 0, 1, 0)}
                    BackgroundColor3={Color3.fromRGB(30, 30, 30)}
                    BorderSizePixel={0}
                >
                    <textlabel
                        Size={new UDim2(1, 0, 1, 0)}
                        BackgroundTransparency={1}
                        Text="🎮 Main Game Content Loaded!"
                        TextColor3={Color3.fromRGB(255, 255, 255)}
                        TextScaled={true}
                        Font={Enum.Font.GothamBold}
                    />
                </frame>
            )}
        </>
    );
}
