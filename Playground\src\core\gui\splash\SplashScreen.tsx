import * as React from "@rbxts/react";
import { COLORS, SIZES, TYPOGRAPHY, BORDER_RADIUS } from "../../design";
import { ZIndexManager } from "../layout/ZIndexManager";
import { Label } from "../label/Label";
import { ContainerFrame, VerticalFrame } from "../frame";
import { Overlay } from "../overlay/Overlay";

interface SplashScreenProps {
  isVisible: boolean;
  loadingProgress: number; // 0 to 1
  loadingText: string;
  onLoadingComplete?: () => void;
}

export function SplashScreen(props: SplashScreenProps) {
  const [fadeOut, setFadeOut] = React.useState(false);
  const splashZIndex = ZIndexManager.getCurrentZIndex() + 1000; // Ensure highest z-index

  // Handle loading completion
  React.useEffect(() => {
    if (props.loadingProgress >= 1 && props.isVisible) {
      // Start fade out animation
      setFadeOut(true);
      
      // Complete loading after fade animation
      task.delay(1, () => {
        if (props.onLoadingComplete) {
          props.onLoadingComplete();
        }
      });
    }
  }, [props.loadingProgress, props.isVisible, props.onLoadingComplete]);

  if (!props.isVisible && !fadeOut) {
    return <></>;
  }

  const backgroundTransparency = fadeOut ? 1 : 0;
  const contentTransparency = fadeOut ? 1 : 0;

  return (
    <Overlay 
      backgroundColor={Color3.fromHex(COLORS.bg.base)} 
      backgroundTransparency={backgroundTransparency}
      zIndex={splashZIndex}
      fullScreen={true}
    >
      {/* Main splash content */}
      <ContainerFrame
        backgroundColor={COLORS.bg.base}
        backgroundTransparency={contentTransparency}
        size={new UDim2(1, 0, 1, 0)}
        padding={0}
        borderThickness={0}
        autoSize={Enum.AutomaticSize.None}
      >
        {/* Center content */}
        <VerticalFrame
          backgroundColor={COLORS.bg.base}
          backgroundTransparency={1}
          size={new UDim2(0.6, 0, 0.4, 0)}
          position={new UDim2(0.5, 0, 0.5, 0)}
          anchorPoint={new Vector2(0.5, 0.5)}
          spacing={SIZES.padding * 3}
          horizontalAlignment={Enum.HorizontalAlignment.Center}
          padding={SIZES.padding * 2}
        >
          {/* Logo/Title */}
          <Label
            text="🎮 RoboxGames Core"
            fontSize={SIZES.fontSize * 3}
            textColor={COLORS.primary}
            size={new UDim2(1, 0, 0, 60)}
            alignment={Enum.TextXAlignment.Center}
            bold={true}
            layoutOrder={1}
          />

          {/* Subtitle */}
          <Label
            text="Advanced Game Framework"
            fontSize={SIZES.fontSize + 4}
            textColor={COLORS.text.secondary}
            size={new UDim2(1, 0, 0, 30)}
            alignment={Enum.TextXAlignment.Center}
            layoutOrder={2}
          />

          {/* Loading progress bar container */}
          <ContainerFrame
            backgroundColor={COLORS["progress-bg"]}
            backgroundTransparency={0}
            size={new UDim2(1, 0, 0, 8)}
            padding={0}
            borderThickness={0}
            autoSize={Enum.AutomaticSize.None}
            layoutOrder={3}
          >
            <uicorner CornerRadius={new UDim(0, BORDER_RADIUS.sm)} />

            {/* Progress fill */}
            <frame
              BackgroundColor3={Color3.fromHex(COLORS["progress-fill"])}
              Size={new UDim2(props.loadingProgress, 0, 1, 0)}
              Position={new UDim2(0, 0, 0, 0)}
              BorderSizePixel={0}
            >
              <uicorner CornerRadius={new UDim(0, BORDER_RADIUS.sm)} />
            </frame>
          </ContainerFrame>

          {/* Loading text */}
          <Label
            text={props.loadingText}
            fontSize={SIZES.fontSize}
            textColor={COLORS.text.main}
            size={new UDim2(1, 0, 0, 20)}
            alignment={Enum.TextXAlignment.Center}
            layoutOrder={4}
          />

          {/* Progress percentage */}
          <Label
            text={`${math.floor(props.loadingProgress * 100)}%`}
            fontSize={SIZES.fontSize + 2}
            textColor={COLORS.primary}
            size={new UDim2(1, 0, 0, 25)}
            alignment={Enum.TextXAlignment.Center}
            bold={true}
            layoutOrder={5}
          />
        </VerticalFrame>

        {/* Footer */}
        <Label
          text="Powered by @roboxgames/core framework"
          fontSize={SIZES.fontSize - 2}
          textColor={COLORS.text.secondary}
          size={new UDim2(1, 0, 0, 20)}
          position={new UDim2(0, 0, 1, -40)}
          alignment={Enum.TextXAlignment.Center}
        />
      </ContainerFrame>
    </Overlay>
  );
}
