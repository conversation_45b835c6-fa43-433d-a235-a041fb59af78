import * as React from "@rbxts/react";
import { createRoot } from "@rbxts/react-roblox";
import { Players } from "@rbxts/services";
import { BottomLeftGrid } from "./gui/BottomLeftGrid";
import { ActionBarDemo } from "./gui/ActionBarDemo";
import { MovementExample } from "./movement/MovementExample";
import { initializeDebugSystem, initializeClientCore, SplashScreen, useSplashScreen } from "../core";

const version = "v1.3.5";
const player = Players.LocalPlayer;
const playerGui = player.WaitForChild("PlayerGui") as PlayerGui;

// Create a ScreenGui with properties to make it visible
const screenGui = new Instance("ScreenGui", playerGui);
screenGui.ResetOnSpawn = false;
screenGui.IgnoreGuiInset = true;
screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling;
screenGui.DisplayOrder = 100; // Higher than DebugGUI to ensure interactive panels appear on top
screenGui.Name = "MainReactGUI";

print(`🎮 [${tick()}] Main React ScreenGui created with DisplayOrder: ${screenGui.DisplayOrder}`);

// Main App Component with Splash Screen
function MainApp() {
    const { state, startLoading, hide, setupDefaultTasks, manager } = useSplashScreen();
    const [hasStarted, setHasStarted] = React.useState(false);

    // Setup and start loading when component mounts
    React.useEffect(() => {
        if (!hasStarted) {
            // Setup default core framework loading tasks
            setupDefaultTasks();

            // Add custom game loading tasks
            manager.addLoadingTask({
                name: "Loading Game Systems...",
                weight: 2,
                task: async () => {
                    // Initialize debug system for development
                    initializeDebugSystem();

                    // Initialize client core
                    const clientCoreResult = await initializeClientCore();
                    if (clientCoreResult.isError()) {
                        warn(`Failed to initialize client core: ${clientCoreResult.getError().message}`);
                        throw clientCoreResult.getError().message;
                    }
                }
            });

            manager.addLoadingTask({
                name: "Initializing Movement System...",
                weight: 1,
                task: () => {
                    // Initialize movement example for testing
                    new MovementExample();
                }
            });

            manager.addLoadingTask({
                name: "Finalizing Client Setup...",
                weight: 1,
                task: async () => {
                    print(`🔥 Playground client loaded! [${version}]`);
                    print(`🎙️ Voice system available! Use voiceDemo methods in console`);
                }
            });

            // Start the loading process
            startLoading();
            setHasStarted(true);
        }
    }, [hasStarted, setupDefaultTasks, manager, startLoading]);

    const handleLoadingComplete = React.useCallback(() => {
        // Called when loading is complete and splash screen should hide
        hide();
        print("🎉 Splash screen loading completed!");
    }, [hide]);

    return (
        <>
            <SplashScreen
                isVisible={state.isVisible}
                loadingProgress={state.loadingProgress}
                loadingText={state.loadingText}
                onLoadingComplete={handleLoadingComplete}
            />

            {/* Main game UI - only show when splash is hidden */}
            {!state.isVisible && (
                <>
                    <BottomLeftGrid
                        onTestClick={() => print("Test button clicked!")}
                        onHelloClick={() => print("Hello button clicked!")}
                    />
                    <ActionBarDemo />
                </>
            )}
        </>
    );
}

const root = createRoot(screenGui);

// Render the main app with splash screen
root.render(<MainApp />);

// All initialization is now handled by the splash screen system