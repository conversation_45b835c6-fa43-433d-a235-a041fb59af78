-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Lighting = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Lighting
local WeatherSystem = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "WeatherParticleHelper").WeatherSystem
local WeatherSoundSystem = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "WeatherSoundHelper").WeatherSoundSystem
local PhysicsImpactHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "physics", "PhysicsImpactHelper").PhysicsImpactHelper
--[[
	*
	 * WeatherController - Advanced weather system with realistic effects
	 * Provides weather effects with proper particles, sounds, and transitions
	 
]]
local WeatherController
do
	WeatherController = setmetatable({}, {
		__tostring = function()
			return "WeatherController"
		end,
	})
	WeatherController.__index = WeatherController
	function WeatherController.new(...)
		local self = setmetatable({}, WeatherController)
		return self:constructor(...) or self
	end
	function WeatherController:constructor()
		self.currentWeather = "clear"
		self.currentIntensity = 0.5
		self.isTransitioning = false
		print("🌦️ Weather Controller initialized")
	end
	function WeatherController:getInstance()
		if not WeatherController.instance then
			WeatherController.instance = WeatherController.new()
		end
		return WeatherController.instance
	end
	function WeatherController:setWeather(options)
		if self.isTransitioning then
			print("⚠️ Weather transition already in progress, skipping")
			return nil
		end
		local previousWeather = self.currentWeather
		local _condition = options.transitionDuration
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 0
		end
		local transitionDuration = _condition
		print(`🌦️ Setting weather: {previousWeather} -> {options.type}`)
		-- For now, always apply immediately for better testing
		self:applyWeatherImmediate(options)
	end
	function WeatherController:applyWeatherImmediate(options)
		print(`🌦️ Applying weather: {options.type}`)
		self.currentWeather = options.type
		-- Clear existing weather effects
		self:clearWeatherEffects()
		local _exp = options.type
		repeat
			if _exp == "clear" then
				self:setClearWeather()
				break
			end
			if _exp == "rain" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.5
				end
				_self:setRainWeather(_condition)
				break
			end
			if _exp == "heavy_rain" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.8
				end
				_self:setRainWeather(_condition)
				break
			end
			if _exp == "snow" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.5
				end
				_self:setSnowWeather(_condition)
				break
			end
			if _exp == "blizzard" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.9
				end
				_self:setSnowWeather(_condition)
				break
			end
			if _exp == "storm" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.7
				end
				_self:setStormWeather(_condition)
				break
			end
			if _exp == "thunderstorm" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.8
				end
				_self:setThunderstormWeather(_condition)
				break
			end
			if _exp == "fog" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.6
				end
				_self:setFogWeather(_condition)
				break
			end
			if _exp == "sandstorm" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.7
				end
				_self:setSandstormWeather(_condition)
				break
			end
		until true
		print(`✅ Weather changed to: {options.type}`)
	end
	function WeatherController:setClearWeather()
		print("☀️ Setting clear weather")
		-- Clear, bright day lighting
		Lighting.FogEnd = 100000
		Lighting.FogStart = 0
		Lighting.FogColor = Color3.new(0.76, 0.76, 0.76)
		Lighting.Brightness = 1
		Lighting.Ambient = Color3.new(0.5, 0.5, 0.5)
		-- Stop all weather effects
		WeatherSystem:clearAllWeatherEffects()
		WeatherSoundSystem:stopAllWeatherSounds()
		self.currentIntensity = 0
		print("✅ Clear weather set")
	end
	function WeatherController:setRainWeather(intensity)
		print(`🌧️ Setting realistic rain weather with physics - intensity {intensity}`)
		-- Overcast and rainy lighting with realistic atmospheric conditions
		Lighting.FogEnd = math.max(500, 2000 - (intensity * 1500))
		Lighting.FogStart = 0
		Lighting.FogColor = Color3.new(0.6, 0.6, 0.6)
		Lighting.Brightness = math.max(0.3, 0.8 - (intensity * 0.4))
		Lighting.Ambient = Color3.new(0.3, 0.3, 0.4)
		-- Apply realistic rain physics using existing physics system
		-- Real raindrops: 2-9 m/s terminal velocity, affected by wind and air resistance
		local windSpeed = self:calculateRealisticWindSpeed(intensity)
		local gravityMultiplier = self:calculateRainGravityEffect(intensity)
		-- Create physics zones for realistic rain behavior
		if intensity > 0.3 then
			-- Create subtle downward force zones to simulate realistic rain physics
			PhysicsImpactHelper:initialize()
			-- Light gravity zones that affect loose objects (papers, leaves, etc.)
			PhysicsImpactHelper:createGravityZone({
				zoneType = "gravity",
				center = Vector3.new(0, 100, 0),
				radius = 1000,
				intensity = gravityMultiplier,
				gravityMultiplier = gravityMultiplier,
				gravityDirection = Vector3.new(windSpeed * 0.1, -1, 0),
				duration = nil,
				visualEffect = false,
				affectedObjects = { "parts", "debris" },
			})
		end
		-- Create realistic rain particles with proper terminal velocity physics
		WeatherSystem:createRealisticRain(intensity, windSpeed)
		-- Play layered rain ambient sounds with atmospheric effects
		WeatherSoundSystem:playLayeredWeatherAmbient("rain", intensity)
		self.currentIntensity = intensity
		print(`✅ Realistic rain weather set with intensity {intensity}, wind: {windSpeed} m/s`)
	end
	function WeatherController:setSnowWeather(intensity)
		print(`❄️ Setting realistic snow weather with physics - intensity {intensity}`)
		-- Snow lighting conditions - brighter and whiter than rain
		Lighting.FogEnd = math.max(300, 1500 - (intensity * 1000))
		Lighting.FogStart = 0
		Lighting.FogColor = Color3.new(0.9, 0.9, 0.95)
		Lighting.Brightness = math.min(2.0, 1.2 + (intensity * 0.3))
		Lighting.Ambient = Color3.new(0.7, 0.7, 0.8)
		-- Apply realistic snow physics
		-- Real snowflakes: 0.5-2 m/s terminal velocity, much slower than rain
		local snowFallSpeed = self:calculateSnowFallSpeed(intensity)
		local windSpeed = self:calculateRealisticWindSpeed(intensity) * 0.6
		-- Create physics zones for realistic snow behavior
		if intensity > 0.2 then
			PhysicsImpactHelper:initialize()
			-- Very light gravity zones for snow (snowflakes are much lighter)
			PhysicsImpactHelper:createGravityZone({
				zoneType = "gravity",
				center = Vector3.new(0, 120, 0),
				radius = 1200,
				intensity = 0.95,
				gravityMultiplier = 0.95,
				gravityDirection = Vector3.new(windSpeed * 0.2, -1, 0),
				duration = nil,
				visualEffect = false,
				affectedObjects = { "parts", "debris" },
			})
		end
		-- Create realistic snow particles with proper fall speed
		WeatherSystem:createSnowEffect(intensity, windSpeed)
		-- Play layered snow ambient sounds
		WeatherSoundSystem:playLayeredWeatherAmbient("snow", intensity)
		self.currentIntensity = intensity
		print(`✅ Realistic snow weather set with intensity {intensity}, fall speed: {snowFallSpeed} m/s`)
	end
	function WeatherController:setStormWeather(intensity)
		print(`🌪️ Setting storm weather with intensity {intensity}`)
		self:setRainWeather(intensity)
	end
	function WeatherController:setThunderstormWeather(intensity)
		print(`⛈️ Setting thunderstorm weather with intensity {intensity}`)
		self:setRainWeather(intensity)
	end
	function WeatherController:setFogWeather(intensity)
		print(`🌫️ Setting fog weather with intensity {intensity}`)
		self:setRainWeather(intensity)
	end
	function WeatherController:setSandstormWeather(intensity)
		print(`🏜️ Setting sandstorm weather with intensity {intensity}`)
		self:setRainWeather(intensity)
	end
	function WeatherController:getCurrentWeather()
		return self.currentWeather
	end
	function WeatherController:getCurrentIntensity()
		return self.currentIntensity
	end
	function WeatherController:calculateRealisticWindSpeed(intensity)
		-- Real-world wind speeds during rain
		local baseWind = 1
		local maxWind = 15
		return baseWind + (intensity * (maxWind - baseWind))
	end
	function WeatherController:calculateRainGravityEffect(intensity)
		-- Very subtle gravity increase (1.0 = normal, 1.1 = 10% stronger)
		return 1.0 + (intensity * 0.1)
	end
	function WeatherController:calculateSnowFallSpeed(intensity)
		local baseSpeed = 0.5
		local maxSpeed = 2.0
		return baseSpeed + (intensity * (maxSpeed - baseSpeed))
	end
	function WeatherController:clearWeatherEffects()
		print("🧹 Clearing weather effects")
		WeatherSystem:clearAllWeatherEffects()
		WeatherSoundSystem:stopAllWeatherSounds()
	end
	function WeatherController:cleanup()
		print("🧹 Cleaning up weather controller")
		self:clearWeatherEffects()
		self:setClearWeather()
	end
end
return {
	WeatherController = WeatherController,
}
